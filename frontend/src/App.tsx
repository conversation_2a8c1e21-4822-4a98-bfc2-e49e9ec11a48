import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Layout from './components/Layout';
import Home from './pages/Home';
import Team from './pages/Team';
import Research from './pages/Research';
import Projects from './pages/Projects';
import Publications from './pages/Publications';
import News from './pages/News';
import Contact from './pages/Contact';

import './styles/global.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/team" element={<Team />} />
            <Route path="/research" element={<Research />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/publications" element={<Publications />} />
            <Route path="/news" element={<News />} />
            <Route path="/contact" element={<Contact />} />

          </Routes>
        </Layout>
      </Router>
    </ConfigProvider>
  );
};

export default App;
