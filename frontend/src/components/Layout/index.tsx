import React, { useState } from 'react';
import { Layout as Ant<PERSON>ay<PERSON>, <PERSON>u, Button, Dropdown, Drawer } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  TeamOutlined,
  ExperimentOutlined,
  ProjectOutlined,
  BookOutlined,
  FileTextOutlined,
  ContactsOutlined,
  MenuOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import CenteredContainer from '../CenteredContainer';
import type { MenuProps } from 'antd';

const { Header, Content, Footer } = AntLayout;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const menuItems: MenuProps['items'] = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/team',
      icon: <TeamOutlined />,
      label: <Link to="/team">团队成员</Link>,
    },
    {
      key: '/research',
      icon: <ExperimentOutlined />,
      label: <Link to="/research">研究方向</Link>,
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: <Link to="/projects">科研项目</Link>,
    },
    {
      key: '/publications',
      icon: <BookOutlined />,
      label: <Link to="/publications">学术成果</Link>,
    },
    {
      key: '/news',
      icon: <FileTextOutlined />,
      label: <Link to="/news">新闻动态</Link>,
    },
    {
      key: '/contact',
      icon: <ContactsOutlined />,
      label: <Link to="/contact">联系我们</Link>,
    },
  ];

  const mobileMenuItems: MenuProps['items'] = [
    {
      key: 'menu',
      icon: <MenuOutlined />,
      children: menuItems,
    },
  ];

  return (
    <AntLayout style={{ minHeight: '100vh', background: '#fbfbfd' }}>
      {/* 苹果风格导航栏 */}
      <Header
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          height: '72px',
          padding: '0 32px',
          boxShadow: '0 2px 20px rgba(0, 0, 0, 0.08)'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {/* Logo */}
          <Link to="/" style={{ textDecoration: 'none' }}>
            <div style={{
              color: '#1d1d1f',
              fontSize: '1.5rem',
              fontWeight: '700',
              marginRight: '48px',
              letterSpacing: '-0.02em'
            }}>
              课题组网站
            </div>
          </Link>

          {/* 桌面端菜单 */}
          <div className="hidden md:block">
            <Menu
              mode="horizontal"
              selectedKeys={[location.pathname]}
              items={menuItems}
              style={{
                borderBottom: 'none',
                background: 'transparent',
                fontSize: '16px',
                fontWeight: '500'
              }}
              theme="light"
            />
          </div>
        </div>

        {/* 移动端菜单按钮 */}
        <div className="md:hidden">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            style={{
              color: '#1d1d1f',
              fontSize: '18px',
              width: '40px',
              height: '40px',
              borderRadius: '12px'
            }}
          />
        </div>
      </Header>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            课题组网站
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setMobileMenuVisible(false)}
              style={{ color: '#86868b' }}
            />
          </div>
        }
        placement="right"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        width={280}
        closable={false}
        styles={{
          body: { padding: 0 },
          header: { borderBottom: '1px solid #f0f0f0' }
        }}
      >
        <Menu
          mode="vertical"
          selectedKeys={[location.pathname]}
          items={menuItems.map(item => ({
            ...item,
            onClick: () => setMobileMenuVisible(false)
          }))}
          style={{
            border: 'none',
            fontSize: '16px'
          }}
        />
      </Drawer>

      <Content style={{
        padding: '48px 0',
        marginTop: '72px',
        minHeight: 'calc(100vh - 72px - 120px)',
        width: '100%'
      }}>
        <CenteredContainer>
          {children}
        </CenteredContainer>
      </Content>

      {/* 苹果风格页脚 */}
      <Footer style={{
        textAlign: 'center',
        background: '#f2f2f7',
        borderTop: '1px solid rgba(0, 0, 0, 0.08)',
        padding: '48px 32px'
      }}>
        <div style={{
          marginBottom: '16px',
          fontSize: '18px',
          fontWeight: '600',
          color: '#1d1d1f'
        }}>
          课题组网站
        </div>
        <div style={{
          fontSize: '14px',
          color: '#86868b',
          lineHeight: '1.6'
        }}>
          基于 React + Strapi + Ant Design 构建<br />
          © 2024 版权所有
        </div>
      </Footer>
    </AntLayout>
  );
};

export default Layout; 