/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #1d1d1f;
  background-color: #fbfbfd;
  font-size: 16px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 - 苹果风格 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
  background-clip: content-box;
}

/* 链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  width: 100%;
  box-sizing: border-box;
}

/* 页面标题样式 - 苹果风格 */
.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #1d1d1f;
  text-align: center;
  letter-spacing: -0.02em;
}

.page-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  color: #86868b;
  text-align: center;
  margin-bottom: 2rem;
  line-height: 1.4;
}

/* 卡片样式 - 苹果风格 */
.content-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 2rem !important;
    margin-bottom: 2rem !important;
  }

  .content-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  /* 移动端布局调整 */
  .ant-layout-content > div {
    padding: 24px 16px !important;
  }

  .ant-carousel {
    margin: 0 -16px;
  }

  .apple-grid,
  .apple-grid-2,
  .apple-grid-3 {
    padding: 0 !important;
  }
}

/* Ant Design 组件样式覆盖 */
.ant-layout-header {
  background: #001529;
  padding: 0 24px;
}

.ant-layout-content {
  min-height: calc(100vh - 64px - 70px);
  padding: 24px 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.ant-layout-footer {
  background: #f0f2f5;
  text-align: center;
  padding: 24px;
  width: 100%;
}

.ant-menu-horizontal {
  border-bottom: none;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 40px 20px;
  color: #ff4d4f;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 苹果风格按钮 */
.apple-button {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: -0.01em;
}

.apple-button:hover {
  background: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.apple-button-secondary {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

.apple-button-secondary:hover {
  background: rgba(0, 122, 255, 0.15);
  color: #0056cc;
}

/* 苹果风格输入框 */
.apple-input {
  border: 1px solid #d2d2d7;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.apple-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  outline: none;
}

/* 苹果风格标签 */
.apple-tag {
  background: #f2f2f7;
  color: #1d1d1f;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  margin: 4px;
}

.apple-tag-primary {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

/* 苹果风格分割线 */
.apple-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #d2d2d7, transparent);
  margin: 32px 0;
  border: none;
}

/* 苹果风格阴影 */
.apple-shadow-sm {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.apple-shadow-md {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.apple-shadow-lg {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.16);
}

/* 苹果风格渐变背景 */
.apple-gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.apple-gradient-text {
  background: linear-gradient(135deg, #007aff, #5856d6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 苹果风格动画 */
@keyframes apple-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.apple-fade-in {
  animation: apple-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 苹果风格网格布局 */
.apple-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.apple-grid-2 {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.apple-grid-3 {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 响应式网格调整 */
@media (max-width: 768px) {
  .apple-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }

  .apple-grid-2 {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }

  .apple-grid-3 {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .apple-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    justify-items: center;
  }

  .apple-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    justify-items: center;
  }

  .apple-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    justify-items: center;
  }
}

@media (min-width: 1025px) {
  .apple-grid,
  .apple-grid-2,
  .apple-grid-3 {
    justify-items: stretch;
  }
}

/* 页面内容居中 - 最终解决方案 */
.ant-layout-content {
  display: flex !important;
  justify-content: center !important;
  align-items: flex-start !important;
}

.ant-layout-content > div {
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 确保主要组件居中 */
.ant-carousel {
  margin: 0 auto !important;
}

.apple-grid,
.apple-grid-2,
.apple-grid-3 {
  margin: 0 auto !important;
}

.ant-row {
  margin: 0 auto !important;
}

/* 确保标签页内容居中 */
.ant-tabs-content-holder {
  display: flex !important;
  justify-content: center !important;
}

.ant-tabs-tabpane {
  width: 100% !important;
  max-width: 1200px !important;
}

/* 确保内容区域不会超出容器 */
* {
  box-sizing: border-box;
}

/* 最后的强制居中规则 */
body {
  margin: 0;
  padding: 0;
}

#root {
  width: 100%;
  margin: 0 auto;
}

.ant-layout {
  width: 100%;
}

