import React from 'react';
import { Card, Row, Col, Typography, Tag, Progress, Space, Tabs } from 'antd';
import { ProjectOutlined, TeamOutlined, DollarOutlined, CalendarOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const Projects: React.FC = () => {
  const projects = {
    进行中: [
      {
        id: 1,
        title: '基于深度学习的医学图像智能诊断系统',
        description: '本项目旨在开发一套基于深度学习的医学图像智能诊断系统，能够自动识别和分析医学影像中的病变，为医生提供辅助诊断建议。',
        leader: '李教授',
        members: ['张三', '王五', '赵六'],
        funding: '国家自然科学基金',
        budget: 200,
        startDate: '2024-01-01',
        endDate: '2027-12-31',
        status: '进行中',
        category: '国家级',
        progress: 35,
        keywords: ['深度学习', '医学图像', '智能诊断', '计算机视觉'],
        achievements: [
          '完成了数据预处理流程的设计和实现',
          '开发了基于CNN的病变检测算法',
          '在公开数据集上取得了85%的准确率'
        ]
      },
      {
        id: 2,
        title: '多模态情感分析关键技术研究',
        description: '研究融合文本、语音、图像等多种模态信息的情感分析技术，构建更加准确和鲁棒的情感识别系统。',
        leader: '李教授',
        members: ['李四', '孙七'],
        funding: '省自然科学基金',
        budget: 50,
        startDate: '2024-03-01',
        endDate: '2026-02-28',
        status: '进行中',
        category: '省部级',
        progress: 60,
        keywords: ['多模态', '情感分析', '自然语言处理', '计算机视觉'],
        achievements: [
          '构建了多模态情感数据集',
          '设计了跨模态特征融合算法',
          '在多个基准数据集上验证了算法有效性'
        ]
      }
    ],
    已完成: [
      {
        id: 3,
        title: '智能推荐系统算法优化研究',
        description: '针对传统推荐系统存在的问题，研究基于深度学习和强化学习的智能推荐算法，提高推荐准确性和用户满意度。',
        leader: '李教授',
        members: ['张三', '王五'],
        funding: '企业合作项目',
        budget: 100,
        startDate: '2023-01-01',
        endDate: '2024-06-30',
        status: '已完成',
        category: '企业合作',
        progress: 100,
        keywords: ['推荐系统', '深度学习', '强化学习', '算法优化'],
        achievements: [
          '开发了基于深度神经网络的推荐算法',
          '实现了在线学习和实时推荐功能',
          '在电商平台上部署并取得显著效果'
        ]
      }
    ],
    计划中: [
      {
        id: 4,
        title: '边缘计算环境下的实时目标检测系统',
        description: '研究在资源受限的边缘设备上实现实时目标检测的技术，为智能监控、自动驾驶等应用提供解决方案。',
        leader: '李教授',
        members: ['李四', '赵六'],
        funding: '国家重点研发计划',
        budget: 300,
        startDate: '2025-01-01',
        endDate: '2028-12-31',
        status: '计划中',
        category: '国家级',
        progress: 0,
        keywords: ['边缘计算', '目标检测', '实时处理', '模型压缩'],
        achievements: []
      }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '进行中': return 'processing';
      case '已完成': return 'success';
      case '计划中': return 'default';
      default: return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case '国家级': return 'red';
      case '省部级': return 'orange';
      case '企业合作': return 'blue';
      default: return 'default';
    }
  };

  const renderProjectCard = (project: any) => (
    <Col xs={24} lg={12} key={project.id}>
      <Card
        hoverable
        className="content-card"
        title={
          <div>
            <Title level={4} style={{ margin: 0 }}>
              <ProjectOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              {project.title}
            </Title>
          </div>
        }
        extra={
          <Space>
            <Tag color={getStatusColor(project.status)}>{project.status}</Tag>
            <Tag color={getCategoryColor(project.category)}>{project.category}</Tag>
          </Space>
        }
      >
        <Paragraph style={{ marginBottom: 16 }}>
          {project.description}
        </Paragraph>

        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <div>
            <TeamOutlined style={{ marginRight: 8 }} />
            <strong>项目负责人：</strong>{project.leader}
          </div>
          <div>
            <TeamOutlined style={{ marginRight: 8 }} />
            <strong>项目成员：</strong>{project.members.join('、')}
          </div>
          <div>
            <DollarOutlined style={{ marginRight: 8 }} />
            <strong>资助机构：</strong>{project.funding}
          </div>
          <div>
            <DollarOutlined style={{ marginRight: 8 }} />
            <strong>项目经费：</strong>{project.budget}万元
          </div>
          <div>
            <CalendarOutlined style={{ marginRight: 8 }} />
            <strong>项目周期：</strong>{project.startDate} 至 {project.endDate}
          </div>
        </Space>

        {project.status === '进行中' && (
          <div style={{ marginBottom: 16 }}>
            <strong>项目进度：</strong>
            <Progress percent={project.progress} status="active" />
          </div>
        )}

        <div style={{ marginBottom: 16 }}>
          <strong>关键词：</strong>
          <Space wrap style={{ marginTop: 8 }}>
            {project.keywords.map((keyword: string, index: number) => (
              <Tag key={index} color="blue">{keyword}</Tag>
            ))}
          </Space>
        </div>

        {project.achievements.length > 0 && (
          <div>
            <strong>主要成果：</strong>
            <ul style={{ paddingLeft: 20, marginTop: 8 }}>
              {project.achievements.map((achievement: string, index: number) => (
                <li key={index}>{achievement}</li>
              ))}
            </ul>
          </div>
        )}
      </Card>
    </Col>
  );

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Title level={1} style={{
          margin: 0,
          color: '#1d1d1f',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          科研项目
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: '#86868b',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          我们课题组承担了多项国家级、省部级和企业合作项目，在人工智能领域取得了丰硕的研究成果
        </Paragraph>
      </div>

      <div style={{ width: '100%' }}>
        <Tabs
          defaultActiveKey="进行中"
          size="large"
          centered
          style={{ width: '100%' }}
          items={Object.entries(projects).map(([status, projectList]) => ({
            key: status,
            label: status,
            children: (
              <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                <Row gutter={[24, 24]} style={{ width: '100%', maxWidth: '1200px', margin: 0 }}>
                  {projectList.map(renderProjectCard)}
                </Row>
              </div>
            )
          }))}
        />
      </div>

      {/* 项目统计 */}
      <div style={{ marginTop: '64px', width: '100%' }}>
        <div className="apple-grid" style={{ gap: '24px' }}>
          {[
            { title: '总项目数', value: 4, color: '#007aff' },
            { title: '进行中', value: 2, color: '#34c759' },
            { title: '已完成', value: 1, color: '#ff9500' },
            { title: '总经费(万元)', value: 650, color: '#5856d6' }
          ].map((stat, index) => (
            <Card
              key={index}
              style={{
                borderRadius: '20px',
                border: 'none',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                textAlign: 'center'
              }}
              bodyStyle={{ padding: '32px' }}
            >
              <Title level={2} style={{ color: stat.color, margin: '0 0 8px 0' }}>
                {stat.value}
              </Title>
              <Paragraph style={{ margin: 0, color: '#86868b', fontSize: '16px' }}>
                {stat.title}
              </Paragraph>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Projects; 