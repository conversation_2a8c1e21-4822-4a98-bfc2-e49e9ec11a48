import React from 'react';
import { Card, Row, Col, Typography, Tag, Space, List, Tabs } from 'antd';
import { BookOutlined, FileTextOutlined, TrophyOutlined, GlobalOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const Publications: React.FC = () => {
  const publications = {
    论文: [
      {
        id: 1,
        title: 'Deep Learning for Medical Image Analysis: A Comprehensive Survey',
        authors: ['张三', '李教授', '王五'],
        journal: 'IEEE Transactions on Medical Imaging',
        publicationDate: '2024-06-15',
        doi: '10.1109/TMI.2024.123456',
        impactFactor: 8.5,
        citations: 25,
        type: '论文',
        keywords: ['深度学习', '医学图像', '计算机视觉'],
        abstract: '本文全面综述了深度学习在医学图像分析中的应用，包括图像分割、分类、检测等任务。'
      },
      {
        id: 2,
        title: 'Multi-Modal Sentiment Analysis Using Transformer Networks',
        authors: ['李四', '李教授'],
        journal: 'Proceedings of ACL 2024',
        publicationDate: '2024-05-20',
        doi: '10.18653/v1/2024.acl-long.123',
        type: '论文',
        keywords: ['多模态', '情感分析', 'Transformer'],
        abstract: '提出了一种基于Transformer的多模态情感分析方法，在多个数据集上取得了优异效果。'
      }
    ],
    专利: [
      {
        id: 3,
        title: '一种基于深度学习的医学图像智能诊断方法',
        authors: ['李教授', '张三'],
        publicationDate: '2024-03-10',
        patentNumber: 'CN202410123456.7',
        type: '专利',
        status: '已授权',
        keywords: ['深度学习', '医学诊断', '图像处理'],
        description: '本发明公开了一种基于深度学习的医学图像智能诊断方法，能够自动识别医学影像中的病变。'
      }
    ],
    获奖: [
      {
        id: 4,
        title: '全国大学生人工智能竞赛一等奖',
        authors: ['王五', '赵六'],
        publicationDate: '2024-04-15',
        type: '获奖',
        organization: '中国人工智能学会',
        keywords: ['竞赛获奖', '人工智能'],
        description: '在全国大学生人工智能竞赛中获得一等奖，展示了团队在AI领域的实力。'
      }
    ],
    报告: [
      {
        id: 5,
        title: '深度学习在计算机视觉中的最新进展',
        authors: ['李教授'],
        publicationDate: '2024-07-10',
        type: '报告',
        venue: 'CVPR 2024 Workshop',
        location: '西雅图，美国',
        keywords: ['深度学习', '计算机视觉', '学术报告'],
        description: '在CVPR 2024会议上做特邀报告，分享了深度学习在计算机视觉领域的最新研究成果。'
      }
    ]
  };

  const renderPublicationItem = (item: any) => (
    <List.Item key={item.id}>
      <Card hoverable style={{ width: '100%' }}>
        <div style={{ marginBottom: 12 }}>
          <Title level={5} style={{ margin: 0, color: '#1890ff' }}>
            {item.title}
          </Title>
        </div>
        
        <div style={{ marginBottom: 8 }}>
          <strong>作者：</strong>{item.authors.join('、')}
        </div>
        
        <div style={{ marginBottom: 8 }}>
          <strong>发表时间：</strong>{item.publicationDate}
        </div>

        {item.journal && (
          <div style={{ marginBottom: 8 }}>
            <strong>期刊/会议：</strong>{item.journal}
          </div>
        )}

        {item.doi && (
          <div style={{ marginBottom: 8 }}>
            <strong>DOI：</strong>
            <a href={`https://doi.org/${item.doi}`} target="_blank" rel="noopener noreferrer">
              {item.doi}
            </a>
          </div>
        )}

        {item.impactFactor && (
          <div style={{ marginBottom: 8 }}>
            <strong>影响因子：</strong>{item.impactFactor}
          </div>
        )}

        {item.citations && (
          <div style={{ marginBottom: 8 }}>
            <strong>引用次数：</strong>{item.citations}
          </div>
        )}

        {item.patentNumber && (
          <div style={{ marginBottom: 8 }}>
            <strong>专利号：</strong>{item.patentNumber}
          </div>
        )}

        {item.status && (
          <div style={{ marginBottom: 8 }}>
            <strong>状态：</strong>
            <Tag color={item.status === '已授权' ? 'green' : 'orange'}>{item.status}</Tag>
          </div>
        )}

        {item.organization && (
          <div style={{ marginBottom: 8 }}>
            <strong>颁发机构：</strong>{item.organization}
          </div>
        )}

        {item.venue && (
          <div style={{ marginBottom: 8 }}>
            <strong>会议/地点：</strong>{item.venue} {item.location && `(${item.location})`}
          </div>
        )}

        <div style={{ marginBottom: 8 }}>
          <strong>关键词：</strong>
          <Space wrap style={{ marginTop: 4 }}>
            {item.keywords.map((keyword: string, index: number) => (
              <Tag key={index} color="blue">{keyword}</Tag>
            ))}
          </Space>
        </div>

        <div>
          <strong>摘要/描述：</strong>
          <Paragraph style={{ margin: '4px 0 0 0' }}>
            {item.abstract || item.description}
          </Paragraph>
        </div>
      </Card>
    </List.Item>
  );

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Title level={1} style={{
          margin: 0,
          color: '#1d1d1f',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          学术成果
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: '#86868b',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          我们课题组在人工智能领域发表了大量高质量的学术论文，获得了多项专利和奖项
        </Paragraph>
      </div>

      <div style={{ width: '100%' }}>
        <Tabs
          defaultActiveKey="论文"
          size="large"
          centered
          style={{ width: '100%' }}
          items={Object.entries(publications).map(([type, items]) => ({
            key: type,
            label: (
              <span>
                {type === '论文' && <FileTextOutlined />}
                {type === '专利' && <BookOutlined />}
                {type === '获奖' && <TrophyOutlined />}
                {type === '报告' && <GlobalOutlined />}
                <span style={{ marginLeft: '8px' }}>{type}</span>
              </span>
            ),
            children: (
              <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                <List
                  style={{ width: '100%', maxWidth: '1200px' }}
                  dataSource={items}
                  renderItem={renderPublicationItem}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  }}
                />
              </div>
            )
          }))}
        />
      </div>

      {/* 统计信息 */}
      <Card title="成果统计" className="content-card" style={{ marginTop: 32 }}>
        <Row gutter={[24, 24]}>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#1890ff', margin: 0 }}>2</Title>
                <Paragraph style={{ margin: 0 }}>发表论文</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#52c41a', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>授权专利</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#faad14', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>获奖成果</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#722ed1', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>学术报告</Paragraph>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Publications; 