import React from 'react';
import { Row, Col, Card, Carousel, Typography, Space, Statistic, Button, Badge } from 'antd';
import {
  TeamOutlined,
  ExperimentOutlined,
  ProjectOutlined,
  BookOutlined,
  ArrowRightOutlined,
  StarOutlined,
  TrophyOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  // 轮播图数据
  const carouselItems = [
    {
      id: 1,
      title: '探索未来科技',
      subtitle: '致力于前沿人工智能研究',
      description: '我们专注于计算机视觉、自然语言处理和机器学习等前沿领域，推动科技创新与人才培养',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1200&h=600&fit=crop',
      gradient: 'linear-gradient(135deg, rgba(0, 122, 255, 0.8), rgba(88, 86, 214, 0.8))',
    },
    {
      id: 2,
      title: '突破性研究成果',
      subtitle: '在顶级期刊发表重要论文',
      description: '团队在CVPR、NeurIPS等国际顶级会议发表多篇高质量论文，获得学术界广泛认可',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=600&fit=crop',
      gradient: 'linear-gradient(135deg, rgba(82, 196, 26, 0.8), rgba(24, 144, 255, 0.8))',
    },
    {
      id: 3,
      title: '卓越团队文化',
      subtitle: '培养下一代科研精英',
      description: '营造开放包容的学术环境，通过丰富的学术交流和团队活动，培养具有国际视野的优秀人才',
      image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=1200&h=600&fit=crop',
      gradient: 'linear-gradient(135deg, rgba(114, 46, 209, 0.8), rgba(245, 34, 45, 0.8))',
    },
  ];

  // 统计数据
  const statistics = [
    {
      title: '团队成员',
      value: 15,
      icon: <TeamOutlined />,
      color: '#007aff',
      description: '包括导师、博士生、硕士生等'
    },
    {
      title: '研究方向',
      value: 5,
      icon: <ExperimentOutlined />,
      color: '#34c759',
      description: '涵盖AI核心技术领域'
    },
    {
      title: '在研项目',
      value: 8,
      icon: <ProjectOutlined />,
      color: '#ff9500',
      description: '国家级、省部级重点项目'
    },
    {
      title: '发表论文',
      value: 50,
      icon: <BookOutlined />,
      color: '#ff3b30',
      description: '顶级期刊和会议论文'
    },
  ];

  return (
    <div>
      {/* 英雄区域 - 苹果风格 */}
      <div style={{ marginBottom: '80px' }}>
        <Carousel
          autoplay
          effect="fade"
          style={{
            borderRadius: '24px',
            overflow: 'hidden',
            boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)'
          }}
        >
          {carouselItems.map((item) => (
            <div key={item.id}>
              <div
                style={{
                  height: '600px',
                  background: `${item.gradient}, url(${item.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  textAlign: 'center',
                  position: 'relative',
                }}
              >
                <div style={{
                  maxWidth: '800px',
                  padding: '0 32px',
                  zIndex: 2
                }}>
                  <div style={{
                    fontSize: '20px',
                    fontWeight: '500',
                    marginBottom: '16px',
                    opacity: 0.9,
                    letterSpacing: '0.02em'
                  }}>
                    {item.subtitle}
                  </div>
                  <Title
                    level={1}
                    style={{
                      color: 'white',
                      marginBottom: '24px',
                      fontSize: '3.5rem',
                      fontWeight: '700',
                      letterSpacing: '-0.02em',
                      lineHeight: '1.1'
                    }}
                  >
                    {item.title}
                  </Title>
                  <Paragraph style={{
                    color: 'white',
                    fontSize: '20px',
                    lineHeight: '1.6',
                    opacity: 0.95,
                    maxWidth: '600px',
                    margin: '0 auto'
                  }}>
                    {item.description}
                  </Paragraph>
                </div>
              </div>
            </div>
          ))}
        </Carousel>
      </div>

      {/* 统计数据 - 苹果风格 */}
      <div style={{ marginBottom: '80px' }}>
        <div className="apple-grid" style={{ gap: '32px' }}>
          {statistics.map((stat, index) => (
            <Card
              key={index}
              style={{
                borderRadius: '20px',
                border: 'none',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                cursor: 'pointer'
              }}
              hoverable
              bodyStyle={{ padding: '32px' }}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}10)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 20px',
                  fontSize: '24px',
                  color: stat.color
                }}>
                  {stat.icon}
                </div>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  color: '#1d1d1f',
                  marginBottom: '8px',
                  letterSpacing: '-0.02em'
                }}>
                  {stat.value}
                </div>
                <div style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1d1d1f',
                  marginBottom: '8px'
                }}>
                  {stat.title}
                </div>
                <div style={{
                  fontSize: '14px',
                  color: '#86868b',
                  lineHeight: '1.4'
                }}>
                  {stat.description}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* 课题组简介和研究方向 - 苹果风格 */}
      <div style={{ marginBottom: '80px' }}>
        <Row gutter={[48, 48]}>
          <Col xs={24} lg={12}>
            <Card
              style={{
                borderRadius: '24px',
                border: 'none',
                height: '100%',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
              }}
              bodyStyle={{ padding: '40px' }}
            >
              <div style={{ marginBottom: '24px' }}>
                <Badge
                  count={<StarOutlined style={{ color: '#007aff' }} />}
                  style={{ backgroundColor: 'transparent' }}
                >
                  <Title level={3} style={{
                    margin: 0,
                    color: '#1d1d1f',
                    fontWeight: '700',
                    fontSize: '1.75rem'
                  }}>
                    课题组简介
                  </Title>
                </Badge>
              </div>

              <Paragraph style={{
                fontSize: '16px',
                lineHeight: '1.7',
                color: '#1d1d1f',
                marginBottom: '20px'
              }}>
                我们是一个专注于人工智能和机器学习研究的课题组，致力于在计算机视觉、自然语言处理、
                强化学习等领域进行前沿研究。课题组由经验丰富的导师和充满活力的学生组成，
                拥有先进的实验设备和丰富的学术资源。
              </Paragraph>

              <Paragraph style={{
                fontSize: '16px',
                lineHeight: '1.7',
                color: '#1d1d1f',
                marginBottom: '32px'
              }}>
                我们的研究目标是解决实际应用中的关键问题，推动人工智能技术的发展，
                培养具有创新能力和实践经验的优秀人才。
              </Paragraph>

              <Link to="/team">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    background: '#007aff',
                    borderColor: '#007aff',
                    borderRadius: '12px',
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '500',
                    boxShadow: '0 4px 16px rgba(0, 122, 255, 0.3)'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  查看团队成员
                </Button>
              </Link>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              style={{
                borderRadius: '24px',
                border: 'none',
                height: '100%',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
              }}
              bodyStyle={{ padding: '40px' }}
            >
              <div style={{ marginBottom: '24px' }}>
                <Badge
                  count={<RocketOutlined style={{ color: '#34c759' }} />}
                  style={{ backgroundColor: 'transparent' }}
                >
                  <Title level={3} style={{
                    margin: 0,
                    color: '#1d1d1f',
                    fontWeight: '700',
                    fontSize: '1.75rem'
                  }}>
                    研究方向
                  </Title>
                </Badge>
              </div>

              <Space direction="vertical" style={{ width: '100%' }} size={24}>
                {[
                  {
                    title: '计算机视觉',
                    description: '图像识别、目标检测、视频分析等',
                    color: '#007aff'
                  },
                  {
                    title: '自然语言处理',
                    description: '文本分类、机器翻译、对话系统等',
                    color: '#34c759'
                  },
                  {
                    title: '强化学习',
                    description: '智能决策、机器人控制、游戏AI等',
                    color: '#ff9500'
                  }
                ].map((item, index) => (
                  <div key={index} style={{
                    padding: '16px 20px',
                    borderRadius: '12px',
                    background: `${item.color}08`,
                    border: `1px solid ${item.color}20`
                  }}>
                    <div style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#1d1d1f',
                      marginBottom: '8px'
                    }}>
                      {item.title}
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#86868b',
                      lineHeight: '1.5'
                    }}>
                      {item.description}
                    </div>
                  </div>
                ))}
              </Space>

              <Link to="/research">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    background: '#34c759',
                    borderColor: '#34c759',
                    borderRadius: '12px',
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '500',
                    marginTop: '32px',
                    boxShadow: '0 4px 16px rgba(52, 199, 89, 0.3)'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  查看详细研究方向
                </Button>
              </Link>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 最新动态 - 苹果风格 */}
      <div style={{ marginBottom: '80px' }}>
        <div style={{
          textAlign: 'center',
          marginBottom: '48px'
        }}>
          <Badge
            count={<TrophyOutlined style={{ color: '#ff9500' }} />}
            style={{ backgroundColor: 'transparent' }}
          >
            <Title level={2} style={{
              margin: 0,
              color: '#1d1d1f',
              fontWeight: '700',
              fontSize: '2.5rem',
              letterSpacing: '-0.02em'
            }}>
              最新动态
            </Title>
          </Badge>
          <Paragraph style={{
            fontSize: '18px',
            color: '#86868b',
            marginTop: '16px',
            marginBottom: 0
          }}>
            了解我们的最新研究进展和团队动态
          </Paragraph>
        </div>

        <div className="apple-grid-3" style={{ width: '100%' }}>
          {[
            {
              title: '课题组获得国家自然科学基金项目',
              date: '2024-12-15',
              category: '项目获批',
              content: '我们课题组成功申请到国家自然科学基金面上项目，项目经费200万元，将用于深度学习在医学图像分析中的应用研究。',
              color: '#007aff',
              icon: <ProjectOutlined />
            },
            {
              title: '团队成员在国际顶级会议发表论文',
              date: '2024-12-10',
              category: '学术成果',
              content: '课题组博士生张三的论文被CVPR 2024接收，这是计算机视觉领域的顶级会议，标志着我们在该领域的研究水平达到了国际先进水平。',
              color: '#34c759',
              icon: <BookOutlined />
            },
            {
              title: '举办学术交流活动',
              date: '2024-12-05',
              category: '学术交流',
              content: '课题组邀请了来自MIT的知名学者进行学术报告，分享了在人工智能领域的最新研究成果，为团队成员提供了宝贵的学习机会。',
              color: '#ff9500',
              icon: <TeamOutlined />
            }
          ].map((news, index) => (
            <Card
              key={index}
              style={{
                borderRadius: '20px',
                border: 'none',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                cursor: 'pointer',
                height: '100%'
              }}
              hoverable
              bodyStyle={{ padding: '32px' }}
            >
              <div style={{ marginBottom: '16px' }}>
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  padding: '6px 12px',
                  borderRadius: '20px',
                  background: `${news.color}15`,
                  color: news.color,
                  fontSize: '12px',
                  fontWeight: '600',
                  marginBottom: '16px'
                }}>
                  {news.icon}
                  <span style={{ marginLeft: '6px' }}>{news.category}</span>
                </div>
                <div style={{
                  fontSize: '14px',
                  color: '#86868b',
                  marginBottom: '12px'
                }}>
                  {news.date}
                </div>
              </div>

              <Title level={4} style={{
                color: '#1d1d1f',
                fontWeight: '600',
                fontSize: '18px',
                lineHeight: '1.4',
                marginBottom: '16px'
              }}>
                {news.title}
              </Title>

              <Paragraph style={{
                fontSize: '14px',
                color: '#86868b',
                lineHeight: '1.6',
                margin: 0
              }}>
                {news.content}
              </Paragraph>
            </Card>
          ))}
        </div>

        <div style={{ textAlign: 'center', marginTop: '48px' }}>
          <Link to="/news">
            <Button
              size="large"
              style={{
                background: 'transparent',
                borderColor: '#007aff',
                color: '#007aff',
                borderRadius: '12px',
                height: '48px',
                fontSize: '16px',
                fontWeight: '500',
                padding: '0 32px'
              }}
              icon={<ArrowRightOutlined />}
            >
              查看更多动态
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home; 