{"kind": "collectionType", "collectionName": "team_members", "info": {"singularName": "team-member", "pluralName": "team-members", "displayName": "team_member"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "title": {"type": "string", "required": true}, "role": {"type": "enumeration", "required": true, "enum": ["Mentor", "Alumni", "PhD", "Master", "Bachelor"]}, "avatar": {"type": "media", "multiple": true, "allowedTypes": ["images"]}, "researchDirection": {"type": "blocks", "required": true}, "email": {"type": "email", "required": true}, "phone": {"type": "string"}, "website": {"type": "string"}, "education": {"type": "blocks"}, "bio": {"type": "blocks"}, "enrollmentYear": {"type": "integer"}, "graduationYear": {"type": "integer"}, "company": {"type": "string"}, "position": {"type": "string"}, "sortOrder": {"type": "integer"}}}